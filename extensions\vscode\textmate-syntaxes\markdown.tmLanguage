<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>md</string>
		<string>mdown</string>
		<string>markdown</string>
		<string>markdn</string>
	</array>
	<key>keyEquivalent</key>
	<string>^~M</string>
	<key>name</key>
	<string>Markdown</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>include</key>
			<string>#block</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>block</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#separator</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#heading</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#blockquote</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#lists</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#raw_block</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_css</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_basic</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_ini</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_java</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_lua</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_makefile</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_perl</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_r</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_ruby</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_php</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_sql</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_vs_net</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_xml</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_xsl</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_yaml</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_dosbatch</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_clojure</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_coffee</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_c</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_diff</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_dockerfile</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_git_commit</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_git_rebase</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_groovy</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_jade</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_js</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_js_regexp</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_json</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_less</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_objc</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_perl6</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_powershell</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_python</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_regexp_python</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_shell</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_ts</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_tsx</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#fenced_code_block_csharp</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#link-def</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#html</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#paragraph</string>
				</dict>
			</array>
			<key>repository</key>
			<dict>
				<key>blockquote</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)(&gt;) ?</string>
					<key>captures</key>
					<dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>beginning.punctuation.definition.quote.markdown</string>
						</dict>
					</dict>
					<key>name</key>
					<string>markup.quote.markdown</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#block</string>
						</dict>
					</array>
					<key>while</key>
					<string>(^|\G)(&gt;) ?</string>
				</dict>
				<key>heading</key>
				<dict>
					<key>begin</key>
					<string>(?:^|\G)(#{1,6})\s*(?=[\S[^#]])</string>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.heading.markdown</string>
						</dict>
					</dict>
					<key>contentName</key>
					<string>entity.name.section.markdown</string>
					<key>end</key>
					<string>\s*(#{1,6})?$\n?</string>
					<key>name</key>
					<string>markup.heading.${1/(#)(#)?(#)?(#)?(#)?(#)?/${6:?6:${5:?5:${4:?4:${3:?3:${2:?2:1}}}}}/}.markdown</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#inline</string>
						</dict>
					</array>
				</dict>
				<key>heading-setext</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>^(={3,})(?=[ \t]*$\n?)</string>
							<key>name</key>
							<string>markup.heading.setext.1.markdown</string>
						</dict>
						<dict>
							<key>match</key>
							<string>^(-{3,})(?=[ \t]*$\n?)</string>
							<key>name</key>
							<string>markup.heading.setext.2.markdown</string>
						</dict>
					</array>
				</dict>
				<key>html</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>(^|\G)\s*(&lt;!--)</string>
							<key>end</key>
							<string>(--&gt;)</string>
							<key>name</key>
							<string>comment.block.html</string>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.comment.html</string>
								</dict>
								<key>2</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.comment.html</string>
								</dict>
							</dict>
						</dict>
						<dict>
							<key>begin</key>
							<string>(^|\G)\s*(?=&lt;/?(address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h1|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|pre|p|param|script|section|source|style|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(\s|$|/?&gt;))</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>text.html.basic</string>
								</dict>
							</array>
							<key>while</key>
							<string>^(?!\s*$)</string>
						</dict>
						<dict>
							<key>begin</key>
							<string>(^|\G)\s*(?=(&lt;[a-zA-Z0-9\-].*&gt;|&lt;/[a-zA-Z0-9\-]&gt;)\s*$)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>text.html.basic</string>
								</dict>
							</array>
							<key>while</key>
							<string>^(?!\s*$)</string>
						</dict>
					</array>
				</dict>
				<key>link-def</key>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.constant.markdown</string>
						</dict>
						<key>10</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.markdown</string>
						</dict>
						<key>11</key>
						<dict>
							<key>name</key>
							<string>string.other.link.description.title.markdown</string>
						</dict>
						<key>12</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.markdown</string>
						</dict>
						<key>13</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.markdown</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>constant.other.reference.link.markdown</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.constant.markdown</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.key-value.markdown</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.link.markdown</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>markup.underline.link.markdown</string>
						</dict>
						<key>7</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.link.markdown</string>
						</dict>
						<key>8</key>
						<dict>
							<key>name</key>
							<string>string.other.link.description.title.markdown</string>
						</dict>
						<key>9</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.markdown</string>
						</dict>
					</dict>
					<key>match</key>
					<string>^(?x:
									\s*						# Leading whitespace
									(\[)(.+?)(\])(:)		# Reference name
									[ \t]*					# Optional whitespace
									(&lt;?)(\S+?)(&gt;?)			# The url
									[ \t]*					# Optional whitespace
									(?:
										  ((\().+?(\)))		# Match title in quotes…
										| ((").+?("))		# or in parens.
									)?						# Title is optional
									\s*						# Optional whitespace
									$
								)</string>
					<key>name</key>
					<string>meta.link.reference.def.markdown</string>
				</dict>
				<key>list_paragraph</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)(?=\S)(?![*+-]\s|[0-9]+\.\s)</string>
					<key>name</key>
					<string>meta.paragraph.markdown</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#inline</string>
						</dict>
						<dict>
							<key>include</key>
							<string>text.html.basic</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#heading-setext</string>
						</dict>
					</array>
					<key>while</key>
					<string>(^|\G)(?!\s*$|#|[ ]{0,3}([-*_][ ]{2,}){3,}[ \t]*$\n?|&gt;|[ ]{0,3}[*+-]|[ ]{0,3}[0-9]+\.)</string>
				</dict>
				<key>lists</key>
				<dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>(^|\G)([ ]{0,3})([*+-])([ ]{1,3}|\t)</string>
							<key>beginCaptures</key>
							<dict>
								<key>3</key>
								<dict>
									<key>name</key>
									<string>beginning.punctuation.definition.list.markdown</string>
								</dict>
							</dict>
							<key>comment</key>
							<string>Currently does not support un-indented second lines.</string>
							<key>name</key>
							<string>markup.list.unnumbered.markdown</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#list_paragraph</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#block</string>
								</dict>
							</array>
							<key>while</key>
							<string>(^|\G)([ ]{4}|\t)</string>
						</dict>
						<dict>
							<key>begin</key>
							<string>(^|\G)([ ]{0,3})([0-9]+\.)([ ]{1,3}|\t)</string>
							<key>beginCaptures</key>
							<dict>
								<key>3</key>
								<dict>
									<key>name</key>
									<string>beginning.punctuation.definition.list.markdown</string>
								</dict>
							</dict>
							<key>name</key>
							<string>markup.list.numbered.markdown</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#list_paragraph</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#block</string>
								</dict>
							</array>
							<key>while</key>
							<string>(^|\G)([ ]{4}|\t)</string>
						</dict>
					</array>
				</dict>
				<key>paragraph</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)[ ]{0,3}(?=\S)</string>
					<key>name</key>
					<string>meta.paragraph.markdown</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#inline</string>
						</dict>
						<dict>
							<key>include</key>
							<string>text.html.basic</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#heading-setext</string>
						</dict>
					</array>
					<key>while</key>
					<!-- seperator    [ ]{0,3}([-*_][ ]{0,2}\2){2,}[ \t]*$\n? -->
					<!-- list         [ ]{0,3}[*+-]([ ]{1,3}|\t) -->
					<!-- both are folded together in the expression below -->
					<string>(^|\G)(?!\s*$|#|[ ]{0,3}((([*_][ ]{0,2}\2){2,}[ \t]*$\n?)|([*+-]([ ]{1,3}|\t)))|\s*\[.+?\]:|&gt;)</string>
				</dict>
				<key>raw_block</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)([ ]{4}|\t)</string>
					<key>name</key>
					<string>markup.raw.block.markdown</string>
					<key>while</key>
					<string>(^|\G)([ ]{4}|\t)</string>
				</dict>
				<key>fenced_code_block_css</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(css|css.erb)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.css</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_basic</key>
				<dict>
					<key>begin</key>
						<string>(^|\G)\s*([`~]{3,})\s*(html|htm|shtml|xhtml|inc|tmpl|tpl)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>text.html.basic</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_ini</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(ini|conf)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.ini</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_java</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(java|bsh)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.java</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_lua</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(lua)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.lua</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_makefile</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(Makefile|makefile|GNUmakefile|OCamlMakefile)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.makefile</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_perl</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(perl|pl|pm|pod|t|PL|psgi|vcl)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.perl</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_r</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(R|r|s|S|Rprofile)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.r</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_ruby</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(ruby|rb|rbx|rjs|Rakefile|rake|cgi|fcgi|gemspec|irbrc|Capfile|ru|prawn|Cheffile|Gemfile|Guardfile|Hobofile|Vagrantfile|Appraisals|Rantfile|Berksfile|Berksfile.lock|Thorfile|Puppetfile)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.ruby</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_php</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(php|php3|php4|php5|phpt|phtml|aw|ctp)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>text.html.php</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_sql</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(sql|ddl|dml)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.sql</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_vs_net</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(vb)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.asp.vb.net</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_xml</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(xml|xsd|tld|jsp|pt|cpt|dtml|rss|opml)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>text.xml</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_xsl</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(xsl|xslt)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>text.xml.xsl</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_yaml</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(yaml|yml)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.yaml</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_dosbatch</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(bat|batch)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.dosbatch</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_clojure</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(clj|cljs|clojure)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.clojure</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_coffee</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(coffee|Cakefile|coffee.erb)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.coffee</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_c</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(c|h)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.c</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_diff</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(patch|diff|rej)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.diff</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_dockerfile</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(dockerfile|Dockerfile)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.dockerfile</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_git_commit</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(COMMIT_EDITMSG|MERGE_MSG)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>text.git-commit</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_git_rebase</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(git-rebase-todo)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>text.git-rebase</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_groovy</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(groovy|gvy)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.groovy</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_jade</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(jade)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>text.jade</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_js</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(js|jsx|javascript)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.js</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_js_regexp</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(regexp)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.js.regexp</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_json</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(json|sublime-settings|sublime-menu|sublime-keymap|sublime-mousemap|sublime-theme|sublime-build|sublime-project|sublime-completions)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.json</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_less</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(less)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.css.less</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_objc</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(objectivec|mm|objc|obj-c|m|h)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.objc</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_perl6</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(perl6|p6|pl6|pm6|nqp)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.perl.6</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_powershell</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(powershell|ps1|psm1|psd1)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.powershell</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_python</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(python|py|py3|rpy|pyw|cpy|SConstruct|Sconstruct|sconstruct|SConscript|gyp|gypi)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.python</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_regexp_python</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(re)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.regexp.python</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_shell</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(shell|sh|bash|zsh|bashrc|bash_profile|bash_login|profile|bash_logout|.textmate_init)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.shell</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_ts</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(typescript|ts)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.ts</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_tsx</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(tsx)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)[ ]{0,3}(\2)\s*\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.tsx</string>
						</dict>
					</array>
				</dict>
				<key>fenced_code_block_csharp</key>
				<dict>
					<key>begin</key>
					<string>(^|\G)\s*([`~]{3,})\s*(cs|csharp|c#)\s*$</string>
					<key>name</key>
					<string>markup.fenced_code.block.markdown</string>
					<key>end</key>
					<string>(^|\G)(\2)\n</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>source.cs</string>
						</dict>
					</array>
				</dict>
				<key>separator</key>
				<dict>
					<key>match</key>
					<string>(^|\G)[ ]{0,3}([*-_])([ ]{0,2}\2){2,}[ \t]*$\n?</string>
					<key>name</key>
					<string>meta.separator.markdown</string>
				</dict>
			</dict>
		</dict>
		<key>inline</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#ampersand</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#bracket</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#bold</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#italic</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#raw</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#escape</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#image-inline</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#image-ref</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#link-email</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#link-inet</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#link-inline</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#link-ref</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#link-ref-literal</string>
				</dict>
			</array>
			<key>repository</key>
			<dict>
				<key>ampersand</key>
				<dict>
					<key>comment</key>
					<string>
						Markdown will convert this for us. We match it so that the
						HTML grammar will not mark it up as invalid.
					</string>
					<key>match</key>
					<string>&amp;(?!([a-zA-Z0-9]+|#[0-9]+|#x[0-9a-fA-F]+);)</string>
					<key>name</key>
					<string>meta.other.valid-ampersand.markdown</string>
				</dict>
				<key>bold</key>
				<dict>
					<key>begin</key>
					<string>(?x)
								(\*\*|__)(?=\S)								# Open
								(?=
									(
										&lt;[^&gt;]*+&gt;							# HTML tags
									  | (?&lt;raw&gt;`+)([^`]|(?!(?&lt;!`)\k&lt;raw&gt;(?!`))`)*+\k&lt;raw&gt;
																			# Raw
									  | \\[\\`*_{}\[\]()#.!+\-&gt;]?+			# Escapes
									  | \[
										(
												(?&lt;square&gt;					# Named group
													[^\[\]\\]				# Match most chars
												  | \\.						# Escaped chars
												  | \[ \g&lt;square&gt;*+ \]		# Nested brackets
												)*+
											\]
											(
												(							# Reference Link
													[ ]?					# Optional space
													\[[^\]]*+\]				# Ref name
												)
											  | (							# Inline Link
													\(						# Opening paren
														[ \t]*+				# Optional whtiespace
														&lt;?(.*?)&gt;?			# URL
														[ \t]*+				# Optional whtiespace
														(					# Optional Title
															(?&lt;title&gt;['"])
															(.*?)
															\k&lt;title&gt;
														)?
													\)
												)
											)
										)
									  | (?!(?&lt;=\S)\1).						# Everything besides
																			# style closer
									)++
									(?&lt;=\S)\1								# Close
								)
							</string>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.bold.markdown</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?&lt;=\S)(\1)</string>
					<key>name</key>
					<string>markup.bold.markdown</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>applyEndPatternLast</key>
							<integer>1</integer>
							<key>begin</key>
							<string>(?=&lt;[^&gt;]*?&gt;)</string>
							<key>end</key>
							<string>(?&lt;=&gt;)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>text.html.basic</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>include</key>
							<string>#escape</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#ampersand</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#bracket</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#raw</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#italic</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#image-inline</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#link-inline</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#link-inet</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#link-email</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#image-ref</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#link-ref-literal</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#link-ref</string>
						</dict>
					</array>
				</dict>
				<key>bracket</key>
				<dict>
					<key>comment</key>
					<string>
						Markdown will convert this for us. We match it so that the
						HTML grammar will not mark it up as invalid.
					</string>
					<key>match</key>
					<string>&lt;(?![a-z/?\$!])</string>
					<key>name</key>
					<string>meta.other.valid-bracket.markdown</string>
				</dict>
				<key>escape</key>
				<dict>
					<key>match</key>
					<string>\\[-`*_#+.!(){}\[\]\\&gt;]</string>
					<key>name</key>
					<string>constant.character.escape.markdown</string>
				</dict>
				<key>image-inline</key>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.markdown</string>
						</dict>
						<key>10</key>
						<dict>
							<key>name</key>
							<string>string.other.link.description.title.markdown</string>
						</dict>
						<key>11</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.markdown</string>
						</dict>
						<key>12</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.markdown</string>
						</dict>
						<key>13</key>
						<dict>
							<key>name</key>
							<string>string.other.link.description.title.markdown</string>
						</dict>
						<key>14</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.markdown</string>
						</dict>
						<key>15</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.markdown</string>
						</dict>
						<key>16</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.metadata.markdown</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>string.other.link.description.markdown</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.markdown</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>invalid.illegal.whitespace.markdown</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.metadata.markdown</string>
						</dict>
						<key>7</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.link.markdown</string>
						</dict>
						<key>8</key>
						<dict>
							<key>name</key>
							<string>markup.underline.link.image.markdown</string>
						</dict>
						<key>9</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.link.markdown</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?x:
						(\!\[)((?&lt;square&gt;[^\[\]\\]|\\.|\[\g&lt;square&gt;*+\])*+)(\])
													# Match the link text.
						([ ])?						# Space not allowed
						(\()						# Opening paren for url
							(&lt;?)(\S+?)(&gt;?)			# The url
							[ \t]*					# Optional whitespace
							(?:
								  ((\().+?(\)))		# Match title in parens…
								| ((").+?("))		# or in quotes.
							)?						# Title is optional
							\s*						# Optional whitespace
						(\))
					 )</string>
					<key>name</key>
					<string>meta.image.inline.markdown</string>
				</dict>
				<key>image-ref</key>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.markdown</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>string.other.link.description.markdown</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.markdown</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.constant.markdown</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>constant.other.reference.link.markdown</string>
						</dict>
						<key>7</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.constant.markdown</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\!\[)((?&lt;square&gt;[^\[\]\\]|\\.|\[\g&lt;square&gt;*+\])*+)(\])[ ]?(\[)(.*?)(\])</string>
					<key>name</key>
					<string>meta.image.reference.markdown</string>
				</dict>
				<key>italic</key>
				<dict>
					<key>begin</key>
					<string>(?x)
								(\*|_)(?=\S)								# Open
								(?=
									(
										&lt;[^&gt;]*+&gt;							# HTML tags
									  | (?&lt;raw&gt;`+)([^`]|(?!(?&lt;!`)\k&lt;raw&gt;(?!`))`)*+\k&lt;raw&gt;
																			# Raw
									  | \\[\\`*_{}\[\]()#.!+\-&gt;]?+			# Escapes
									  | \[
										(
												(?&lt;square&gt;					# Named group
													[^\[\]\\]				# Match most chars
												  | \\.						# Escaped chars
												  | \[ \g&lt;square&gt;*+ \]		# Nested brackets
												)*+
											\]
											(
												(							# Reference Link
													[ ]?					# Optional space
													\[[^\]]*+\]				# Ref name
												)
											  | (							# Inline Link
													\(						# Opening paren
														[ \t]*+				# Optional whtiespace
														&lt;?(.*?)&gt;?			# URL
														[ \t]*+				# Optional whtiespace
														(					# Optional Title
															(?&lt;title&gt;['"])
															(.*?)
															\k&lt;title&gt;
														)?
													\)
												)
											)
										)
									  | \1\1								# Must be bold closer
									  | (?!(?&lt;=\S)\1).						# Everything besides
																			# style closer
									)++
									(?&lt;=\S)\1								# Close
								)
							</string>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.italic.markdown</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?&lt;=\S)(\1)((?!\1)|(?=\1\1))</string>
					<key>name</key>
					<string>markup.italic.markdown</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>applyEndPatternLast</key>
							<integer>1</integer>
							<key>begin</key>
							<string>(?=&lt;[^&gt;]*?&gt;)</string>
							<key>end</key>
							<string>(?&lt;=&gt;)</string>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>text.html.basic</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>include</key>
							<string>#escape</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#ampersand</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#bracket</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#raw</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#bold</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#image-inline</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#link-inline</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#link-inet</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#link-email</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#image-ref</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#link-ref-literal</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#link-ref</string>
						</dict>
					</array>
				</dict>
				<key>link-email</key>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.link.markdown</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>markup.underline.link.markdown</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.link.markdown</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(&lt;)((?:mailto:)?[-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(&gt;)</string>
					<key>name</key>
					<string>meta.link.email.lt-gt.markdown</string>
				</dict>
				<key>link-inet</key>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.link.markdown</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>markup.underline.link.markdown</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.link.markdown</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(&lt;)((?:https?|ftp)://.*?)(&gt;)</string>
					<key>name</key>
					<string>meta.link.inet.markdown</string>
				</dict>
				<key>link-inline</key>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.markdown</string>
						</dict>
						<key>10</key>
						<dict>
							<key>name</key>
							<string>string.other.link.description.title.markdown</string>
						</dict>
						<key>11</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.markdown</string>
						</dict>
						<key>12</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.markdown</string>
						</dict>
						<key>13</key>
						<dict>
							<key>name</key>
							<string>string.other.link.description.title.markdown</string>
						</dict>
						<key>14</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.markdown</string>
						</dict>
						<key>15</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.markdown</string>
						</dict>
						<key>16</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.metadata.markdown</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>string.other.link.title.markdown</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.markdown</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>invalid.illegal.whitespace.markdown</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.metadata.markdown</string>
						</dict>
						<key>7</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.link.markdown</string>
						</dict>
						<key>8</key>
						<dict>
							<key>name</key>
							<string>markup.underline.link.markdown</string>
						</dict>
						<key>9</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.link.markdown</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(?x:
						(\[)((?&lt;square&gt;[^\[\]\\]|\\.|\[\g&lt;square&gt;*+\])*+)(\])
													# Match the link text.
						([ ])?						# Space not allowed
						(\()						# Opening paren for url
							(&lt;?)(.*?)(&gt;?)			# The url
							[ \t]*					# Optional whitespace
							(?:
								  ((\().+?(\)))		# Match title in parens…
								| ((").+?("))		# or in quotes.
							)?						# Title is optional
							\s*						# Optional whitespace
						(\))
					 )</string>
					<key>name</key>
					<string>meta.link.inline.markdown</string>
				</dict>
				<key>link-ref</key>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.markdown</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>string.other.link.title.markdown</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.markdown</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.constant.begin.markdown</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>constant.other.reference.link.markdown</string>
						</dict>
						<key>7</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.constant.end.markdown</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\[)((?&lt;square&gt;[^\[\]\\]|\\.|\[\g&lt;square&gt;*+\])*+)(\])[ ]?(\[)([^\]]*+)(\])</string>
					<key>name</key>
					<string>meta.link.reference.markdown</string>
				</dict>
				<key>link-ref-literal</key>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.begin.markdown</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>string.other.link.title.markdown</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.string.end.markdown</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.constant.begin.markdown</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.constant.end.markdown</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\[)((?&lt;square&gt;[^\[\]\\]|\\.|\[\g&lt;square&gt;*+\])*+)(\])[ ]?(\[)(\])</string>
					<key>name</key>
					<string>meta.link.reference.literal.markdown</string>
				</dict>
				<key>raw</key>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.raw.markdown</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.raw.markdown</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(`+)([^`]|(?!(?&lt;!`)\1(?!`))`)*+(\1)</string>
					<key>name</key>
					<string>markup.inline.raw.markdown</string>
				</dict>
			</dict>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>text.html.markdown</string>
	<key>uuid</key>
	<string>0A1D9874-B448-11D9-BD50-000D93B6E43C</string>
</dict>
</plist>
