{"name": "MagicRegExp", "scopeName": "source.regexp.python", "fileTypes": ["re"], "uuid": "39e15186-71e6-11e5-b82c-7c6d62900c7c", "patterns": [{"include": "#regexp-expression"}], "repository": {"regexp-base-expression": {"patterns": [{"name": "support.other.match.any.regexp", "match": "\\."}, {"name": "support.other.match.begin.regexp", "match": "\\^"}, {"name": "support.other.match.end.regexp", "match": "\\$"}, {"name": "keyword.operator.quantifier.regexp", "match": "[+*?]\\??"}, {"name": "keyword.operator.disjunction.regexp", "match": "\\|"}, {"name": "keyword.operator.quantifier.regexp", "match": "(?x)\n  \\{(\n    \\d+ | \\d+,(\\d+)? | ,\\d+\n  )\\}\n"}, {"include": "#regexp-escape-sequence"}]}, "regexp-backreference-number": {"name": "meta.backreference.regexp", "match": "(\\\\[1-9]\\d?)", "captures": {"1": {"name": "entity.name.tag.backreference.regexp"}}}, "regexp-backreference": {"name": "meta.backreference.named.regexp", "match": "(?x)\n  (\\()  (\\?P= \\w+(?:\\s+[[:alnum:]]+)?)  (\\))\n", "captures": {"1": {"name": "punctuation.parenthesis.backreference.named.begin.regexp support.other.parenthesis.regexp"}, "2": {"name": "entity.name.tag.named.backreference.regexp"}, "3": {"name": "punctuation.parenthesis.backreference.named.end.regexp support.other.parenthesis.regexp"}}}, "regexp-flags": {"name": "storage.modifier.flag.regexp", "match": "\\(\\?[aiLmsux]+\\)"}, "regexp-escape-special": {"name": "support.other.escape.special.regexp", "match": "\\\\([AbBdDsSwWZ])"}, "regexp-escape-character": {"name": "constant.character.escape.regexp", "match": "(?x)\n  \\\\ (\n        x[0-9A-Fa-f]{2}\n        | 0[0-7]{1,2}\n        | [0-7]{3}\n     )\n"}, "regexp-escape-unicode": {"name": "constant.character.unicode.regexp", "match": "(?x)\n  \\\\ (\n        u[0-9A-Fa-f]{4}\n        | U[0-9A-Fa-f]{8}\n     )\n"}, "regexp-escape-catchall": {"name": "constant.character.escape.regexp", "match": "\\\\(.|\\n)"}, "regexp-escape-sequence": {"patterns": [{"include": "#regexp-escape-special"}, {"include": "#regexp-escape-character"}, {"include": "#regexp-escape-unicode"}, {"include": "#regexp-backreference-number"}, {"include": "#regexp-escape-catchall"}]}, "regexp-charecter-set-escapes": {"patterns": [{"name": "constant.character.escape.regexp", "match": "\\\\[abfnrtv\\\\]"}, {"include": "#regexp-escape-special"}, {"name": "constant.character.escape.regexp", "match": "\\\\([0-7]{1,3})"}, {"include": "#regexp-escape-character"}, {"include": "#regexp-escape-unicode"}, {"include": "#regexp-escape-catchall"}]}, "regexp-expression": {"patterns": [{"include": "#regexp-base-expression"}, {"include": "#regexp-character-set"}, {"include": "#regexp-comments"}, {"include": "#regexp-flags"}, {"include": "#regexp-named-group"}, {"include": "#regexp-backreference"}, {"include": "#regexp-lookahead"}, {"include": "#regexp-lookahead-negative"}, {"include": "#regexp-lookbehind"}, {"include": "#regexp-lookbehind-negative"}, {"include": "#regexp-conditional"}, {"include": "#regexp-parentheses-non-capturing"}, {"include": "#regexp-parentheses"}]}, "regexp-character-set": {"patterns": [{"match": "(?x)\n  \\[ \\^? \\] (?! .*?\\])\n"}, {"name": "meta.character.set.regexp", "begin": "(\\[)(\\^)?(\\])?", "end": "(\\])", "beginCaptures": {"1": {"name": "constant.other.set.regexp punctuation.character.set.begin.regexp"}, "2": {"name": "keyword.operator.negation.regexp"}, "3": {"name": "constant.character.set.regexp"}}, "endCaptures": {"1": {"name": "constant.other.set.regexp punctuation.character.set.end.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-charecter-set-escapes"}, {"name": "constant.character.set.regexp", "match": "[^\\n]"}]}]}, "regexp-named-group": {"name": "meta.named.regexp", "begin": "(?x)\n  (\\()  (\\?P <\\w+(?:\\s+[[:alnum:]]+)?>)\n", "end": "(\\))", "beginCaptures": {"1": {"name": "punctuation.parenthesis.named.begin.regexp support.other.parenthesis.regexp"}, "2": {"name": "entity.name.tag.named.group.regexp"}}, "endCaptures": {"1": {"name": "punctuation.parenthesis.named.end.regexp support.other.parenthesis.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-comments": {"name": "comment.regexp", "begin": "\\(\\?#", "end": "(\\))", "beginCaptures": {"0": {"name": "punctuation.comment.begin.regexp"}}, "endCaptures": {"1": {"name": "punctuation.comment.end.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#codetags"}]}, "regexp-lookahead": {"begin": "(\\()\\?=", "end": "(\\))", "beginCaptures": {"0": {"name": "keyword.operator.lookahead.regexp"}, "1": {"name": "punctuation.parenthesis.lookahead.begin.regexp"}}, "endCaptures": {"1": {"name": "punctuation.parenthesis.lookahead.end.regexp keyword.operator.lookahead.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-lookahead-negative": {"begin": "(\\()\\?!", "end": "(\\))", "beginCaptures": {"0": {"name": "keyword.operator.lookahead.negative.regexp"}, "1": {"name": "punctuation.parenthesis.lookahead.begin.regexp"}}, "endCaptures": {"1": {"name": "punctuation.parenthesis.lookahead.end.regexp keyword.operator.lookahead.negative.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-lookbehind": {"begin": "(\\()\\?<=", "end": "(\\))", "beginCaptures": {"0": {"name": "keyword.operator.lookbehind.regexp"}, "1": {"name": "punctuation.parenthesis.lookbehind.begin.regexp"}}, "endCaptures": {"1": {"name": "punctuation.parenthesis.lookbehind.end.regexp keyword.operator.lookbehind.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-lookbehind-negative": {"begin": "(\\()\\?<!", "end": "(\\))", "beginCaptures": {"0": {"name": "keyword.operator.lookbehind.negative.regexp"}, "1": {"name": "punctuation.parenthesis.lookbehind.begin.regexp"}}, "endCaptures": {"1": {"name": "punctuation.parenthesis.lookbehind.end.regexp keyword.operator.lookbehind.negative.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-conditional": {"begin": "(\\()\\?\\((\\w+(?:\\s+[[:alnum:]]+)?|\\d+)\\)", "end": "(\\))", "beginCaptures": {"0": {"name": "keyword.operator.conditional.regexp"}, "1": {"name": "punctuation.parenthesis.conditional.begin.regexp"}}, "endCaptures": {"1": {"name": "punctuation.parenthesis.conditional.end.regexp keyword.operator.conditional.negative.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-parentheses-non-capturing": {"begin": "\\(\\?:", "end": "(\\))", "beginCaptures": {"0": {"name": "punctuation.parenthesis.non-capturing.begin.regexp support.other.parenthesis.regexp"}}, "endCaptures": {"1": {"name": "punctuation.parenthesis.non-capturing.end.regexp support.other.parenthesis.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-parentheses": {"begin": "\\(", "end": "(\\))", "beginCaptures": {"0": {"name": "punctuation.parenthesis.begin.regexp support.other.parenthesis.regexp"}}, "endCaptures": {"1": {"name": "punctuation.parenthesis.end.regexp support.other.parenthesis.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}}, "version": "https://github.com/MagicStack/MagicPython/commit/a45287d159f82256c768ad2e1114ca9751d28670"}