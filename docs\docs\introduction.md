---
title: Introduction
description: Create, share, and use custom AI code assistants
slug: /
keywords:
  [
    continue,
    <PERSON>,
    code assistant,
    autocomplete,
    chat,
    VS Code,
    JetBrains,
    introduction,
  ]
---

![Continue Logo](../static/img/intro.png)

**Continue enables developers to create, share, and use custom AI code assistants with our open-source [VS Code](https://marketplace.visualstudio.com/items?itemName=Continue.continue) and [JetBrains](https://plugins.jetbrains.com/plugin/22707-continue-extension) extensions and [hub of models, rules, prompts, docs, and other building blocks](https://hub.continue.dev)**

- [Chat](chat/how-to-use-it) to understand and iterate on code in the sidebar
- [Autocomplete](autocomplete/how-to-use-it) to receive inline code suggestions as you type
- [Edit](edit/how-to-use-it) to modify code without leaving your current file
- [Agent](agent/how-to-use-it) to make more substantial changes to your codebase
