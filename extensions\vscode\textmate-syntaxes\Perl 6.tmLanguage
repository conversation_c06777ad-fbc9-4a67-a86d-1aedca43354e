<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>p6</string>
		<string>pl6</string>
		<string>pm6</string>
		<string>nqp</string>
	</array>
	<key>firstLineMatch</key>
	<string>(^#!.*\b(perl6|nqp)\b)|use\s+v6</string>
	<key>keyEquivalent</key>
	<string>^~P</string>
	<key>name</key>
	<string>Perl 6</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>begin</key>
			<string>^=begin</string>
			<key>end</key>
			<string>^=end</string>
			<key>name</key>
			<string>comment.block.perl</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(^[ \t]+)?(?=#)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.whitespace.comment.leading.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?!\G)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>#</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.perl</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\n</string>
					<key>name</key>
					<string>comment.line.number-sign.perl</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.class.perl.6</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>entity.name.type.class.perl.6</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(class|enum|grammar|knowhow|module|package|role|slang|subset)(\s+)(((?:::|')?(?:([a-zA-Z_\x{C0}-\x{FF}\$])([a-zA-Z0-9_\x{C0}-\x{FF}\\$]|[\-'][a-zA-Z0-9_\x{C0}-\x{FF}\$])*))+)</string>
			<key>name</key>
			<string>meta.class.perl.6</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(?&lt;=\s)'</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>'</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.single.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\['\\]</string>
					<key>name</key>
					<string>constant.character.escape.perl</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>"</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.perl</string>
				</dict>
			</dict>
			<key>end</key>
			<string>"</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.perl</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\[abtnfre"\\]</string>
					<key>name</key>
					<string>constant.character.escape.perl</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>q(q|to|heredoc)*\s*:?(q|to|heredoc)*\s*/(.+)/</string>
			<key>end</key>
			<string>\3</string>
			<key>name</key>
			<string>string.quoted.single.heredoc.perl</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*{{</string>
			<key>end</key>
			<string>}}</string>
			<key>name</key>
			<string>string.quoted.double.heredoc.brace.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_brace_string_content</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*\(\(</string>
			<key>end</key>
			<string>\)\)</string>
			<key>name</key>
			<string>string.quoted.double.heredoc.paren.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_paren_string_content</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*\[\[</string>
			<key>end</key>
			<string>\]\]</string>
			<key>name</key>
			<string>string.quoted.double.heredoc.bracket.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_bracket_string_content</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*{</string>
			<key>end</key>
			<string>}</string>
			<key>name</key>
			<string>string.quoted.single.heredoc.brace.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_brace_string_content</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*/</string>
			<key>end</key>
			<string>/</string>
			<key>name</key>
			<string>string.quoted.single.heredoc.slash.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_slash_string_content</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*\(</string>
			<key>end</key>
			<string>\)</string>
			<key>name</key>
			<string>string.quoted.single.heredoc.paren.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_paren_string_content</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*\[</string>
			<key>end</key>
			<string>\]</string>
			<key>name</key>
			<string>string.quoted.single.heredoc.bracket.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_bracket_string_content</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*'</string>
			<key>end</key>
			<string>'</string>
			<key>name</key>
			<string>string.quoted.single.heredoc.single.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_single_string_content</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>(q|Q)(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*:?(x|exec|w|words|ww|quotewords|v|val|q|single|qq|double|s|scalar|a|array|h|hash|f|function|c|closure|b|blackslash|regexp|substr|trans|codes|p|path)*\s*"</string>
			<key>end</key>
			<string>"</string>
			<key>name</key>
			<string>string.quoted.single.heredoc.double.perl</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_double_string_content</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>match</key>
			<string>\b\$\w+\b</string>
			<key>name</key>
			<string>variable.other.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(macro|sub|submethod|method|multi|proto|only|rule|token|regex|category)\b</string>
			<key>name</key>
			<string>storage.type.declare.routine.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(self)\b</string>
			<key>name</key>
			<string>variable.language.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(use|require)\b</string>
			<key>name</key>
			<string>keyword.other.include.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(if|else|elsif|unless)\b</string>
			<key>name</key>
			<string>keyword.control.conditional.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(let|my|our|state|temp|has|constant)\b</string>
			<key>name</key>
			<string>storage.type.variable.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(for|loop|repeat|while|until|gather|given)\b</string>
			<key>name</key>
			<string>keyword.control.repeat.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(take|do|when|next|last|redo|return|contend|maybe|defer|default|exit|make|continue|break|goto|leave|async|lift)\b</string>
			<key>name</key>
			<string>keyword.control.flowcontrol.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(is|as|but|trusts|of|returns|handles|where|augment|supersede)\b</string>
			<key>name</key>
			<string>storage.modifier.type.constraints.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(BEGIN|CHECK|INIT|START|FIRST|ENTER|LEAVE|KEEP|UNDO|NEXT|LAST|PRE|POST|END|CATCH|CONTROL|TEMP)\b</string>
			<key>name</key>
			<string>meta.function.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(die|fail|try|warn)\b</string>
			<key>name</key>
			<string>keyword.control.control-handlers.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(prec|irs|ofs|ors|export|deep|binary|unary|reparsed|rw|parsed|cached|readonly|defequiv|will|ref|copy|inline|tighter|looser|equiv|assoc|required)\b</string>
			<key>name</key>
			<string>storage.modifier.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(NaN|Inf)\b</string>
			<key>name</key>
			<string>constant.numeric.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(oo|fatal)\b</string>
			<key>name</key>
			<string>keyword.other.pragma.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(Object|Any|Junction|Whatever|Capture|MatchSignature|Proxy|Matcher|Package|Module|ClassGrammar|Scalar|Array|Hash|KeyHash|KeySet|KeyBagPair|List|Seq|Range|Set|Bag|Mapping|Void|UndefFailure|Exception|Code|Block|Routine|Sub|MacroMethod|Submethod|Regex|Str|str|Blob|Char|ByteCodepoint|Grapheme|StrPos|StrLen|Version|NumComplex|num|complex|Bit|bit|bool|True|FalseIncreasing|Decreasing|Ordered|Callable|AnyCharPositional|Associative|Ordering|KeyExtractorComparator|OrderingPair|IO|KitchenSink|RoleInt|int|int1|int2|int4|int8|int16|int32|int64Rat|rat|rat1|rat2|rat4|rat8|rat16|rat32|rat64Buf|buf|buf1|buf2|buf4|buf8|buf16|buf32|buf64UInt|uint|uint1|uint2|uint4|uint8|uint16|uint32uint64|Abstraction|utf8|utf16|utf32)\b</string>
			<key>name</key>
			<string>support.type.perl6</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(div|xx|x|mod|also|leg|cmp|before|after|eq|ne|le|lt|not|gt|ge|eqv|ff|fff|and|andthen|or|xor|orelse|extra|lcm|gcd)\b</string>
			<key>name</key>
			<string>keyword.operator.perl</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(\$|@|%|&amp;)(\*|:|!|\^|~|=|\?|(&lt;(?=.+&gt;)))?([a-zA-Z_\x{C0}-\x{FF}\$])([a-zA-Z0-9_\x{C0}-\x{FF}\$]|[\-'][a-zA-Z0-9_\x{C0}-\x{FF}\$])*</string>
			<key>name</key>
			<string>variable.other.identifier.perl.6</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(eager|hyper|substr|index|rindex|grep|map|sort|join|lines|hints|chmod|split|reduce|min|max|reverse|truncate|zip|cat|roundrobin|classify|first|sum|keys|values|pairs|defined|delete|exists|elems|end|kv|any|all|one|wrap|shape|key|value|name|pop|push|shift|splice|unshift|floor|ceiling|abs|exp|log|log10|rand|sign|sqrt|sin|cos|tan|round|strand|roots|cis|unpolar|polar|atan2|pick|chop|p5chop|chomp|p5chomp|lc|lcfirst|uc|ucfirst|capitalize|normalize|pack|unpack|quotemeta|comb|samecase|sameaccent|chars|nfd|nfc|nfkd|nfkc|printf|sprintf|caller|evalfile|run|runinstead|nothing|want|bless|chr|ord|gmtime|time|eof|localtime|gethost|getpw|chroot|getlogin|getpeername|kill|fork|wait|perl|graphs|codes|bytes|clone|print|open|read|write|readline|say|seek|close|opendir|readdir|slurp|spurt|shell|run|pos|fmt|vec|link|unlink|symlink|uniq|pair|asin|atan|sec|cosec|cotan|asec|acosec|acotan|sinh|cosh|tanh|asinh|done|acos|acosh|atanh|sech|cosech|cotanh|sech|acosech|acotanh|asech|ok|nok|plan_ok|dies_ok|lives_ok|skip|todo|pass|flunk|force_todo|use_ok|isa_ok|diag|is_deeply|isnt|like|skip_rest|unlike|cmp_ok|eval_dies_ok|nok_error|eval_lives_ok|approx|is_approx|throws_ok|version_lt|plan|EVAL|succ|pred|times|nonce|once|signature|new|connect|operator|undef|undefine|sleep|from|to|infix|postfix|prefix|circumfix|postcircumfix|minmax|lazy|count|unwrap|getc|pi|e|context|void|quasi|body|each|contains|rewinddir|subst|can|isa|flush|arity|assuming|rewind|callwith|callsame|nextwith|nextsame|attr|eval_elsewhere|none|srand|trim|trim_start|trim_end|lastcall|WHAT|WHERE|HOW|WHICH|VAR|WHO|WHENCE|ACCEPTS|REJECTS|not|true|iterator|by|re|im|invert|flip|gist|flat|tree|is-prime|throws_like|trans)\b</string>
			<key>name</key>
			<string>support.function.perl</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>qq_brace_string_content</key>
		<dict>
			<key>begin</key>
			<string>{</string>
			<key>end</key>
			<string>}</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_brace_string_content</string>
				</dict>
			</array>
		</dict>
		<key>qq_bracket_string_content</key>
		<dict>
			<key>begin</key>
			<string>\[</string>
			<key>end</key>
			<string>\]</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_bracket_string_content</string>
				</dict>
			</array>
		</dict>
		<key>qq_double_string_content</key>
		<dict>
			<key>begin</key>
			<string>"</string>
			<key>end</key>
			<string>"</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_double_string_content</string>
				</dict>
			</array>
		</dict>
		<key>qq_paren_string_content</key>
		<dict>
			<key>begin</key>
			<string>\(</string>
			<key>end</key>
			<string>\)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_paren_string_content</string>
				</dict>
			</array>
		</dict>
		<key>qq_single_string_content</key>
		<dict>
			<key>begin</key>
			<string>'</string>
			<key>end</key>
			<string>'</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_single_string_content</string>
				</dict>
			</array>
		</dict>
		<key>qq_slash_string_content</key>
		<dict>
			<key>begin</key>
			<string>\\/</string>
			<key>end</key>
			<string>\\/</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#qq_slash_string_content</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>source.perl.6</string>
	<key>uuid</key>
	<string>E685440C-0E20-4424-9693-864D5240A269</string>
</dict>
</plist>