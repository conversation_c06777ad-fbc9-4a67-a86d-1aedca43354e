# OpenRouter

OpenRouter 是一个对于商业的和开源的模型统一的接口，给你最好的模型，最好的价格。你可以在 [这里](https://openrouter.ai/signup) 注册，在 [keys 页面](https://openrouter.ai/keys) 创建你的 API key ，然后从 [支持模型列表](https://openrouter.ai/models) 中选择一个模型。

修改 `~/.continue/config.json` 如下：

```json title="config.json"
{
  "models": [
    {
      "title": "OpenRouter LLaMA 70 8B",
      "provider": "openrouter",
      "model": "meta-llama/llama-3-70b-instruct",
      "apiBase": "https://openrouter.ai/api/v1",
      "apiKey": "..."
    }
  ]
}
```

为了使用比如提供者推理或模型路由配置的特性，在你的插件配置中添加这些参数到 `models[].requestsOptions.extraBodyProperties` 字段。

例如，为了抑制额外的长提示词被压缩，你可以明确地关闭这个特性，像这样：

```json title="config.json"
{
  "models": [
    {
      ...
      "requestOptions": {
        "extraBodyProperties": {
          "transforms": []
        }
      }
    }
  ]
}
```

在 [这里](https://openrouter.ai/docs) 了解更多可用的设置。
