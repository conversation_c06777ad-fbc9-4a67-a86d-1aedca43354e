{"fileTypes": ["html", "htm", "shtml", "xhtml", "inc", "tmpl", "tpl"], "firstLineMatch": "<(?i:(!DOCTYPE\\s*)?html)", "injections": {"R:text.html - comment.block": {"comment": "Use R: to ensure this matches after any other injections.", "patterns": [{"match": "<", "name": "invalid.illegal.bad-angle-bracket.html"}]}}, "keyEquivalent": "^~H", "name": "HTML", "patterns": [{"begin": "(<)([a-zA-Z0-9:\\-]++)(?=[^>]*></\\2>)", "beginCaptures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.html"}}, "end": "(>(<)/)(\\2)(>)", "endCaptures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "meta.scope.between-tag-pair.html"}, "3": {"name": "entity.name.tag.html"}, "4": {"name": "punctuation.definition.tag.html"}}, "name": "meta.tag.any.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(<\\?)(xml)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.xml.html"}}, "end": "(\\?>)", "name": "meta.tag.preprocessor.xml.html", "patterns": [{"include": "#tag-generic-attribute"}, {"include": "#string-double-quoted"}, {"include": "#string-single-quoted"}]}, {"begin": "<!--", "captures": {"0": {"name": "punctuation.definition.comment.html"}}, "end": "--\\s*>", "name": "comment.block.html", "patterns": [{"match": "--", "name": "invalid.illegal.bad-comments-or-CDATA.html"}, {"include": "#embedded-code"}]}, {"begin": "<!", "captures": {"0": {"name": "punctuation.definition.tag.html"}}, "end": ">", "name": "meta.tag.sgml.html", "patterns": [{"begin": "(?i:DOCTYPE)", "captures": {"1": {"name": "entity.name.tag.doctype.html"}}, "end": "(?=>)", "name": "meta.tag.sgml.doctype.html", "patterns": [{"match": "\"[^\">]*\"", "name": "string.quoted.double.doctype.identifiers-and-DTDs.html"}]}, {"begin": "\\[CDATA\\[", "end": "]](?=>)", "name": "constant.other.inline-data.html"}, {"match": "(\\s*)(?!--|>)\\S(\\s*)", "name": "invalid.illegal.bad-comments-or-CDATA.html"}]}, {"include": "#embedded-code"}, {"begin": "(?:^\\s+)?(<)((?i:style))\\b(?![^>]*/>)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.style.html"}, "3": {"name": "punctuation.definition.tag.html"}}, "end": "(</)((?i:style))(>)(?:\\s*\\n)?", "contentName": "source.css.embedded.html", "patterns": [{"include": "#tag-stuff"}, {"begin": "(>)", "beginCaptures": {"1": {"name": "punctuation.definition.tag.html"}}, "end": "(?=</(?i:style))", "patterns": [{"include": "#embedded-code"}, {"include": "source.css"}]}]}, {"begin": "(?:^\\s+)?(<)((?i:script))\\b(?![^>]*/>)(?![^>]*(?i:type.?=.?text/((?!javascript).*)))", "beginCaptures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.script.html"}}, "end": "(?<=</(script|SCRIPT))(>)(?:\\s*\\n)?", "endCaptures": {"2": {"name": "punctuation.definition.tag.html"}}, "contentName": "source.js.embedded.html", "patterns": [{"include": "#tag-stuff"}, {"begin": "(?<!</(?:script|SCRIPT))(>)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.script.html"}}, "end": "(</)((?i:script))", "patterns": [{"captures": {"1": {"name": "punctuation.definition.comment.js"}}, "match": "(//).*?((?=</script)|$\\n?)", "name": "comment.line.double-slash.js"}, {"begin": "/\\*", "captures": {"0": {"name": "punctuation.definition.comment.js"}}, "end": "\\*/|(?=</script)", "name": "comment.block.js"}, {"include": "source.js"}]}]}, {"begin": "(</?)((?i:body|head|html)(?=\\s|\\\\|>))", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.structure.any.html"}}, "end": "(>)", "name": "meta.tag.structure.any.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(</?)((?i:address|blockquote|dd|div|section|article|aside|header|footer|nav|menu|dl|dt|fieldset|form|frame|frameset|h1|h2|h3|h4|h5|h6|iframe|noframes|object|ol|p|ul|applet|center|dir|hr|pre)(?=\\s|\\\\|>))", "beginCaptures": {"1": {"name": "punctuation.definition.tag.begin.html"}, "2": {"name": "entity.name.tag.block.any.html"}}, "end": "(>)", "endCaptures": {"1": {"name": "punctuation.definition.tag.end.html"}}, "name": "meta.tag.block.any.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(</?)((?i:a|abbr|acronym|area|b|base|basefont|bdo|big|br|button|caption|cite|code|col|colgroup|del|dfn|em|font|head|html|i|img|input|ins|isindex|kbd|label|legend|li|link|map|meta|noscript|optgroup|option|param|q|s|samp|script|select|small|span|strike|strong|style|sub|sup|table|tbody|td|textarea|tfoot|th|thead|title|tr|tt|u|var)(?=\\s|\\\\|>))", "beginCaptures": {"1": {"name": "punctuation.definition.tag.begin.html"}, "2": {"name": "entity.name.tag.inline.any.html"}}, "end": "((?: ?/)?>)", "endCaptures": {"1": {"name": "punctuation.definition.tag.end.html"}}, "name": "meta.tag.inline.any.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(</?)([a-zA-Z0-9:\\-]+)", "beginCaptures": {"1": {"name": "punctuation.definition.tag.begin.html"}, "2": {"name": "entity.name.tag.other.html"}}, "end": "(>)", "endCaptures": {"1": {"name": "punctuation.definition.tag.end.html"}}, "name": "meta.tag.other.html", "patterns": [{"include": "#tag-stuff"}]}, {"include": "#entities"}, {"match": "<>", "name": "invalid.illegal.incomplete.html"}], "repository": {"embedded-code": {"patterns": [{"include": "#smarty"}, {"include": "#python"}]}, "entities": {"patterns": [{"captures": {"1": {"name": "punctuation.definition.entity.html"}, "3": {"name": "punctuation.definition.entity.html"}}, "match": "(&)([a-zA-Z0-9]+|#[0-9]+|#[xX][0-9a-fA-F]+)(;)", "name": "constant.character.entity.html"}, {"match": "&", "name": "invalid.illegal.bad-ampersand.html"}]}, "python": {"begin": "(?:^\\s*)<\\?python(?!.*\\?>)", "end": "\\?>(?:\\s*$\\n)?", "contentName": "source.python.embedded.html", "patterns": [{"include": "source.python"}]}, "smarty": {"patterns": [{"begin": "(\\{(literal)\\})", "captures": {"1": {"name": "source.smarty.embedded.html"}, "2": {"name": "support.function.built-in.smarty"}}, "end": "(\\{/(literal)\\})"}, {"begin": "{{|{", "disabled": 1, "end": "}}|}", "contentName": "source.smarty.embedded.html", "patterns": [{"include": "source.smarty"}]}]}, "string-double-quoted": {"begin": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.html"}}, "end": "\"", "endCaptures": {"0": {"name": "punctuation.definition.string.end.html"}}, "name": "string.quoted.double.html", "patterns": [{"include": "#embedded-code"}, {"include": "#entities"}]}, "string-single-quoted": {"begin": "'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.html"}}, "end": "'", "endCaptures": {"0": {"name": "punctuation.definition.string.end.html"}}, "name": "string.quoted.single.html", "patterns": [{"include": "#embedded-code"}, {"include": "#entities"}]}, "tag-generic-attribute": {"match": "(?<=[^=])\\b([a-zA-Z0-9:-]+)", "name": "entity.other.attribute-name.html"}, "tag-id-attribute": {"begin": "\\b(id)\\b\\s*(=)", "captures": {"1": {"name": "entity.other.attribute-name.id.html"}, "2": {"name": "punctuation.separator.key-value.html"}}, "end": "(?!\\G)(?<='|\"|[^\\s<>/])", "name": "meta.attribute-with-value.id.html", "patterns": [{"begin": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.html"}}, "contentName": "meta.toc-list.id.html", "end": "\"", "endCaptures": {"0": {"name": "punctuation.definition.string.end.html"}}, "name": "string.quoted.double.html", "patterns": [{"include": "#embedded-code"}, {"include": "#entities"}]}, {"begin": "'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.html"}}, "contentName": "meta.toc-list.id.html", "end": "'", "endCaptures": {"0": {"name": "punctuation.definition.string.end.html"}}, "name": "string.quoted.single.html", "patterns": [{"include": "#embedded-code"}, {"include": "#entities"}]}, {"captures": {"0": {"name": "meta.toc-list.id.html"}}, "match": "(?<==)(?:[^\\s<>/'\"]|/(?!>))+", "name": "string.unquoted.html"}]}, "tag-stuff": {"patterns": [{"include": "#tag-id-attribute"}, {"include": "#tag-generic-attribute"}, {"include": "#string-double-quoted"}, {"include": "#string-single-quoted"}, {"include": "#embedded-code"}, {"include": "#unquoted-attribute"}]}, "unquoted-attribute": {"match": "(?<==)(?:[^\\s<>/'\"]|/(?!>))+", "name": "string.unquoted.html"}}, "scopeName": "text.html.basic", "uuid": "17994EC8-6B1D-11D9-AC3A-000D93589AF6", "version": "https://github.com/textmate/html.tmbundle/commit/9f812c89f4990a98391701caa77824c94860538f"}