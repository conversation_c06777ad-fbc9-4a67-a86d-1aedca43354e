# Cloudflare Workers AI

Cloudflare Workers AI 可以在 Continue 中用来聊天或 tab 自动补全。要设置 Cloudflare Workers AI ，添加以下配置到你的 `config.json` 文件中：

```json title="config.json"
{
  "models": [
    {
      "accountId": "YOUR CLOUDFLARE ACCOUNT ID",
      "apiKey": "YOUR CLOUDFLARE API KEY",
      "contextLength": 2400,
      "completionOptions": {
        "maxTokens": 500
      },
      "model": "@cf/meta/llama-3-8b-instruct", // This can be the name of any model supported by Workers AI
      "provider": "cloudflare",
      "title": "Llama 3 8B"
    },
    {
      "accountId": "YOUR CLOUDFLARE ACCOUNT ID",
      "apiKey": "YOUR CLOUDFLARE API KEY",
      "contextLength": 2400,
      "completionOptions": {
        "maxTokens": 500
      },
      "model": "@hf/thebloke/deepseek-coder-6.7b-instruct-awq",
      "provider": "cloudflare",
      "title": "DeepSeek Coder 6.7b Instruct"
    }
    ...
    "tabAutocompleteModel": {
      "accountId": "YOUR CLOUDFLARE ACCOUNT ID",
      "apiKey": "YOUR CLOUDFLARE API KEY",
      "model": "@hf/thebloke/deepseek-coder-6.7b-base-awq",
      "provider": "cloudflare",
      "title": "DeepSeek 7b"
    },
  ]
}
```

访问 [Cloudflare dashboard](https://dash.cloudflare.com/) 来 [创建 API key](https://developers.cloudflare.com/fundamentals/api/get-started/create-token/) 。

在 Workers AI 重看 [可用的模型](https://developers.cloudflare.com/workers-ai/models/)

[查看代码](https://github.com/continuedev/continue/blob/main/core/llm/llms/Cloudflare.ts)
