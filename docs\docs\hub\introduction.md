---
title: Introduction
description: Introduction
keywords: [continue, hub, introduction]
sidebar_label: Introduction
---

# Introduction

![Continue Hub](/img/hub/models-page.png)

[Continue Hub](https://hub.continue.dev) makes it simple to create custom AI code [assistants](./assistants/intro.md), providing a registry for defining, managing, and sharing assistant [building blocks](./blocks/intro.md).

Assistants can contain several types of blocks, including [models](./blocks/block-types.md#models), [rules](./blocks/block-types.md#rules), [context providers](./blocks/block-types.md#context), [prompts](./blocks/block-types.md#prompts), [docs](./blocks/block-types.md#docs), [data destinations](./blocks/block-types.md#data), and [MCP servers](./blocks/block-types.md#mcp-servers).

Continue Hub also makes it easy for engineering leaders to centrally [configure](./secrets/secret-types.md) and [govern](./governance/org-permissions.md) blocks and assistants for their organization.
