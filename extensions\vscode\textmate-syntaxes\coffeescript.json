{"comment": "CoffeeScript", "fileTypes": ["coffee", "Cakefile", "coffee.erb", "cake", "cjsx", "cson", "iced"], "injections": {"string.regexp.block.coffee": {"patterns": [{"include": "#interpolated_coffee"}, {"include": "#embedded_comment"}]}}, "keyEquivalent": "^~C", "name": "CoffeeScript", "patterns": [{"captures": {"1": {"name": "punctuation.definition.parameters.begin.coffee"}, "2": {"patterns": [{"captures": {"1": {"name": "variable.parameter.function.coffee"}, "2": {"name": "punctuation.separator.key-value.coffee"}, "3": {"name": "string.quoted.double.coffee"}, "4": {"name": "punctuation.definition.string.begin.coffee"}, "5": {"name": "punctuation.definition.string.end.coffee"}, "6": {"name": "string.quoted.single.coffee"}, "7": {"name": "punctuation.definition.string.begin.coffee"}, "8": {"name": "punctuation.definition.string.end.coffee"}}, "match": "(?x)\n\t\t\t\t\t\t\t\t([^()\\s,]+)\n\t\t\t\t\t\t\t\t\\s+(=)\\s+\n\t\t\t\t\t\t\t\t(?:\n\t\t\t\t\t\t\t\t\t((\")[^\"]*(\"))\n\t\t\t\t\t\t\t\t  | ((')[^']*('))\n\t\t\t\t\t\t\t\t)"}, {"match": "[^()\\s,]+", "name": "variable.parameter.function.coffee"}, {"match": ",", "name": "punctuation.separator.arguments.coffee"}]}, "3": {"name": "punctuation.definition.parameters.begin.coffee"}, "4": {"name": "storage.type.function.coffee"}}, "comment": "match stuff like: a -> … ", "match": "(\\()([^()]*?)(\\))\\s*([=-]>)", "name": "meta.inline.function.coffee"}, {"captures": {"1": {"name": "keyword.operator.new.coffee"}, "4": {"name": "storage.type.class.coffee"}, "6": {"name": "entity.name.type.instance.coffee"}, "7": {"name": "entity.name.type.instance.coffee"}}, "match": "(new)\\s+(((class)(\\s+(\\w+(?:\\.\\w*)*))?)|(\\w+(?:\\.\\w*)*))", "name": "meta.class.instance.constructor"}, {"begin": "'''", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.coffee"}}, "end": "'''", "endCaptures": {"0": {"name": "punctuation.definition.string.end.coffee"}}, "name": "string.unquoted.heredoc.coffee"}, {"begin": "\"\"\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.coffee"}}, "end": "\"\"\"", "endCaptures": {"0": {"name": "punctuation.definition.string.end.coffee"}}, "name": "string.quoted.double.heredoc.coffee", "patterns": [{"match": "\\\\.", "name": "constant.character.escape.coffee"}, {"include": "#interpolated_coffee"}]}, {"begin": "(?=`)", "end": "(?<=`)", "name": "meta.embedded.line.coffee", "patterns": [{"begin": "`", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.coffee"}}, "contentName": "source.js", "end": "(`)", "endCaptures": {"0": {"name": "punctuation.definition.string.end.coffee"}, "1": {"name": "source.js"}}, "name": "string.other.embedded.javascript.coffee", "patterns": [{"include": "source.js"}]}]}, {"begin": "(?<!#)###(?!#)", "captures": {"0": {"name": "punctuation.definition.comment.coffee"}}, "end": "###(?:[ \\t]*\\n)", "name": "comment.block.coffee", "patterns": [{"match": "(?<=^|\\s)@\\w*(?=\\s)", "name": "storage.type.annotation.coffeescript"}]}, {"begin": "(^[ \\t]+)?(?=#)", "beginCaptures": {"1": {"name": "punctuation.whitespace.comment.leading.coffee"}}, "end": "(?!\\G)", "patterns": [{"begin": "#", "beginCaptures": {"0": {"name": "punctuation.definition.comment.coffee"}}, "end": "\\n", "name": "comment.line.number-sign.coffee"}]}, {"begin": "/{3}", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.coffee"}}, "end": "(/{3})[imgy]{0,4}", "endCaptures": {"1": {"name": "punctuation.definition.string.end.coffee"}}, "name": "string.regexp.block.coffee", "patterns": [{"include": "source.js.regexp"}]}, {"begin": "/(?![\\s=/*+{}?])", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.coffee"}}, "end": "(/)[igmy]{0,4}(?![a-zA-Z0-9])", "endCaptures": {"1": {"name": "punctuation.definition.string.end.coffee"}}, "name": "string.regexp.coffee", "patterns": [{"include": "source.js.regexp"}]}, {"match": "(?x)\n\t\t\t\t\\b\n\t\t\t\t(?<![\\.\\$])\n\t\t\t\t(\n\t\t\t\t\tbreak\n\t\t\t\t  | by\n\t\t\t\t  | catch\n\t\t\t\t  | continue\n\t\t\t\t  | else\n\t\t\t\t  | finally\n\t\t\t\t  | for\n\t\t\t\t  | in\n\t\t\t\t  | of\n\t\t\t\t  | if\n\t\t\t\t  | return\n\t\t\t\t  | switch\n\t\t\t\t  | then\n\t\t\t\t  | throw\n\t\t\t\t  | try\n\t\t\t\t  | unless\n\t\t\t\t  | when\n\t\t\t\t  | while\n\t\t\t\t  | until\n\t\t\t\t  | loop\n\t\t\t\t  | do\n\t\t\t\t  | (?<=for)\\s+own\n\t\t\t\t)\n\t\t\t\t(?!\\s*:)\n\t\t\t\t\\b\n\t\t\t", "name": "keyword.control.coffee"}, {"match": "(?x)\n                  (and|or|<<|>>>?|(?<!\\()\\/|[=!<>*%+\\-&^])?=(?!>)\n                | [!%^*\\/~?:]\n                | \\-?\\-(?!>)\n                | \\+\\+?\n                | <>\n                | <\n                | >\n                | &&?\n                | \\.\\.\\.?\n                | \\|\\|?\n                | \\b(?<![\\.\\$])(instanceof|new|delete|typeof|and|or|is|isnt|not|super)(?!\\s*:)\\b\n\t\t\t", "name": "keyword.operator.coffee"}, {"match": "(?x)\n\t\t\t\t\\b\n\t\t\t\t(?<![\\.\\$])\n\t\t\t\t(\n\t\t\t\t\t  case\n                    | default\n                    | function\n                    | var\n                    | void\n                    | with\n                    | const\n                    | let\n                    | enum\n                    | export\n                    | import\n                    | native\n                    | __hasProp\n                    | __extends\n                    | __slice\n                    | __bind\n                    | __indexOf\n                    | implements\n                    | interface\n                    | package\n                    | private\n                    | protected\n                    | public\n                    | static\n                    | yield\n                )\n\t\t\t\t(?!\\s*:)\n\t\t\t\t\\b\n\t\t\t", "name": "keyword.other.reserved.coffee"}, {"captures": {"1": {"name": "variable.other.assignment.coffee"}, "4": {"name": "punctuation.separator.key-value"}, "5": {"name": "keyword.operator.coffee"}}, "match": "(?x)\n\t\t\t\t(?:\n\t\t\t\t\t([a-zA-Z\\$_](\\w|\\$|\\.)*)\n\t\t\t\t\t\\s*\n\t\t\t\t\t(?!\\::)\n\t\t\t\t\t((:)|((?:or|and|[-+/&%*?])?=)(?![>=]))\n\t\t\t\t\t(?!\n\t\t\t\t\t\t(\n\t\t\t\t\t\t\t\\s*\\(.*\\)\n\t\t\t\t\t\t)?\n\t\t\t\t\t\t\\s*\n\t\t\t\t\t\t([=-]>)\n\t\t\t\t\t)\n\t\t\t\t)"}, {"begin": "(?<=\\s|^)(\\{)(?=.+?\\}\\s+[:=])", "beginCaptures": {"0": {"name": "keyword.operator.coffee"}}, "end": "(\\}\\s*[:=])", "endCaptures": {"0": {"name": "keyword.operator.coffee"}}, "name": "meta.variable.assignment.destructured.object.coffee", "patterns": [{"include": "#variable_name"}, {"include": "#instance_variable"}, {"include": "#single_quoted_string"}, {"include": "#double_quoted_string"}, {"include": "#numeric"}]}, {"begin": "(?<=\\s|^)(\\[)(?=.+?\\]\\s+[:=])", "beginCaptures": {"0": {"name": "keyword.operator.coffee"}}, "end": "(\\]\\s*[:=])", "endCaptures": {"0": {"name": "keyword.operator.coffee"}}, "name": "meta.variable.assignment.destructured.array.coffee", "patterns": [{"include": "#variable_name"}, {"include": "#instance_variable"}, {"include": "#single_quoted_string"}, {"include": "#double_quoted_string"}, {"include": "#numeric"}]}, {"captures": {"1": {"name": "entity.name.function.coffee"}, "4": {"name": "punctuation.definition.parameters.begin.coffee"}, "5": {"patterns": [{"captures": {"1": {"name": "variable.parameter.function.coffee"}, "2": {"name": "punctuation.separator.key-value.coffee"}, "3": {"name": "string.quoted.double.coffee"}, "4": {"name": "punctuation.definition.string.begin.coffee"}, "5": {"name": "punctuation.definition.string.end.coffee"}, "6": {"name": "string.quoted.single.coffee"}, "7": {"name": "punctuation.definition.string.begin.coffee"}, "8": {"name": "punctuation.definition.string.end.coffee"}}, "match": "(?x)\n\t\t\t\t\t\t\t\t([^()\\s,]+)\n\t\t\t\t\t\t\t\t\\s+(=)\\s+\n\t\t\t\t\t\t\t\t(?:\n\t\t\t\t\t\t\t\t\t((\")[^\"]*(\"))\n\t\t\t\t\t\t\t\t  | ((')[^']*('))\n\t\t\t\t\t\t\t\t)"}, {"match": "[^()\\s,]+", "name": "variable.parameter.function.coffee"}, {"match": ",", "name": "punctuation.separator.arguments.coffee"}]}, "6": {"name": "punctuation.definition.parameters.begin.coffee"}, "7": {"name": "storage.type.function.coffee"}}, "match": "(?x)\n\t\t\t\t(?<=^|\\s)\n\t\t\t\t(?=@?[a-zA-Z\\$_])\n\t\t\t\t(\n\t\t\t\t\t@?[a-zA-Z\\$_](\\w|\\$|:|\\.)*\n\t\t\t\t\t\\s*\n\t\t\t\t\t(?=\n\t\t\t\t\t\t[:=]\n\t\t\t\t\t\t(\\s*(\\()(.*)(\\)))?\n\t\t\t\t\t\t\\s*\n\t\t\t\t\t\t([=-]>)\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t", "name": "meta.function.coffee"}, {"match": "(?x)\n\t\t\t\t\\b\n\t\t\t\t(?<!\\.|::)\n\t\t\t\t(true|on|yes)\n\t\t\t\t(?!\\s*[:=][^=])\n\t\t\t\t\\b", "name": "constant.language.boolean.true.coffee"}, {"match": "(?x)\n\t\t\t\t\\b\n\t\t\t\t(?<!\\.|::)\n\t\t\t\t(false|off|no)\n\t\t\t\t(?!\\s*[:=][^=])\n\t\t\t\t\\b", "name": "constant.language.boolean.false.coffee"}, {"match": "@?\\b(?!class|subclass|extends|decodeURI(Component)?|encodeURI(Component)?|eval|parse(Float|Int)|require)\\w+(?=\\s+(?!(of|in|then|is|isnt|and|or|for|else|when|not|if)\\s)(?=(@?\\w+|\\->|\\-\\d|\\[|\\{|\"|'))|(?=\\())", "name": "entity.name.function.coffee"}, {"match": "[=-]>", "name": "storage.type.function.coffee"}, {"match": "(?x)\n\t\t\t\t\\b\n\t\t\t\t(?<!\\.|::)\n\t\t\t\tnull\n\t\t\t\t(?!\\s*[:=][^=])\n\t\t\t\t\\b", "name": "constant.language.null.coffee"}, {"match": "(?x)\n\t\t\t\t\\b\n\t\t\t\t(?<!\\.|::)\n\t\t\t\t(extends)\n\t\t\t\t(?!\\s*[:=][^=])\n\t\t\t\t\\b", "name": "variable.language.coffee"}, {"match": "\\b(?<!\\.)this(?!\\s*[:=])\\b", "name": "variable.language.this.coffee"}, {"captures": {"1": {"name": "storage.type.class.coffee"}, "2": {"name": "keyword.control.inheritance.coffee"}, "3": {"name": "entity.other.inherited-class.coffee"}}, "match": "(?x)\n\t\t\t\t\t(?<=\\s|^|\\[|\\()\n                    (class\\b)\n\t\t\t\t\t(?:\n\t\t\t\t\t\t\\s+(extends)\\s+\n\t\t\t\t\t\t(@?[a-zA-Z\\$\\._][\\w\\.]*)\n\t\t\t\t\t)", "name": "meta.class.coffee"}, {"captures": {"1": {"name": "storage.type.class.coffee"}, "2": {"name": "entity.name.type.class.coffee"}, "3": {"name": "keyword.control.inheritance.coffee"}, "4": {"name": "entity.other.inherited-class.coffee"}}, "match": "(?x)\n\t\t\t        (?<=\\s|^|\\[|\\()\n\t\t\t\t\t(class\\b)\n\t\t\t\t\t\\s+\n\t\t\t\t\t(@?[a-zA-Z\\$_][\\w\\.]*)?\n\t\t\t\t\t(?:\n\t\t\t\t\t\t\\s+(extends)\\s+\n\t\t\t\t\t\t(@?[a-zA-Z\\$\\._][\\w\\.]*)\n\t\t\t\t\t)?", "name": "meta.class.coffee"}, {"match": "\\b(debugger|\\\\)\\b", "name": "keyword.other.coffee"}, {"match": "(?x)\n\t\t\t\t\\b(\n\t\t\t\t\tArray\n\t\t\t\t  | ArrayBuffer\n\t\t\t\t  | Blob\n\t\t\t\t  | Boolean\n\t\t\t\t  | Date\n\t\t\t\t  | document\n\t\t\t\t  | Function\n\t\t\t\t  | Int(8|16|32|64)Array\n\t\t\t\t  | Math\n\t\t\t\t  | Map\n\t\t\t\t  | Number\n\t\t\t\t  | Object\n\t\t\t\t  | Proxy\n\t\t\t\t  | RegExp\n\t\t\t\t  | Set\n\t\t\t\t  | String\n\t\t\t\t  | WeakMap\n\t\t\t\t  | window\n\t\t\t\t  | Uint(8|16|32|64)Array\n\t\t\t\t  | XMLHttpRequest\n\t\t\t\t)\\b", "name": "support.class.coffee"}, {"match": "\\b(console)\\b", "name": "entity.name.type.object.coffee"}, {"match": "((?<=console\\.)(debug|warn|info|log|error|time|timeEnd|assert))\\b", "name": "support.function.console.coffee"}, {"match": "(?x)\n\t\t\t\t\\b(\n\t\t\t\t\tdecodeURI(Component)?\n\t\t\t\t  | encodeURI(Component)?\n\t\t\t\t  | eval\n\t\t\t\t  | parse(Float|Int)\n\t\t\t\t  | require\n\t\t\t\t)\\b", "name": "support.function.coffee"}, {"match": "(?x)\n\t\t\t\t(\n\t\t\t\t\t(?<=\\.)\n\t\t\t\t\t(\n\t\t\t\t\t\tapply\n\t\t\t\t\t  | call\n\t\t\t\t\t  | concat\n\t\t\t\t\t  | every\n\t\t\t\t\t  | filter\n\t\t\t\t\t  | forEach\n\t\t\t\t\t  | from\n\t\t\t\t\t  | hasOwnProperty\n\t\t\t\t\t  | indexOf\n\t\t\t\t\t  | isPrototypeOf\n\t\t\t\t\t  | join\n\t\t\t\t\t  | lastIndexOf\n\t\t\t\t\t  | map\n\t\t\t\t\t  | of\n\t\t\t\t\t  | pop\n\t\t\t\t\t  | propertyIsEnumerable\n\t\t\t\t\t  | push\n\t\t\t\t\t  | reduce(Right)?\n\t\t\t\t\t  | reverse\n\t\t\t\t\t  | shift\n\t\t\t\t\t  | slice\n\t\t\t\t\t  | some\n\t\t\t\t\t  | sort\n\t\t\t\t\t  | splice\n\t\t\t\t\t  | to(Locale)?String\n\t\t\t\t\t  | unshift\n\t\t\t\t\t  | valueOf\n\t\t\t\t\t)\n\t\t\t\t)\\b", "name": "support.function.method.array.coffee"}, {"match": "((?<=Array\\.)(isArray))\\b", "name": "support.function.static.array.coffee"}, {"match": "(?x)\n\t\t\t\t(\n\t\t\t\t\t(?<=Object\\.)\n\t\t\t\t\t(\n\t\t\t\t\t\tcreate\n\t\t\t\t\t  | definePropert(ies|y)\n\t\t\t\t\t  | freeze\n\t\t\t\t\t  | getOwnProperty(Descriptors?|Names)\n\t\t\t\t\t  | getProperty(Descriptor|Names)\n\t\t\t\t\t  | getPrototypeOf\n\t\t\t\t\t  | is(Extensible|Frozen|Sealed)?\n\t\t\t\t\t  | isnt\n\t\t\t\t\t  | keys\n\t\t\t\t\t  | preventExtensions\n\t\t\t\t\t  | seal\n\t\t\t\t\t)\n\t\t\t\t)\\b", "name": "support.function.static.object.coffee"}, {"match": "(?x)\n\t\t\t\t(\n\t\t\t\t\t(?<=Math\\.)\n\t\t\t\t\t(\n\t\t\t\t\t\tabs\n\t\t\t\t\t  | acos\n\t\t\t\t\t  | acosh\n\t\t\t\t\t  | asin\n\t\t\t\t\t  | asinh\n\t\t\t\t\t  | atan\n\t\t\t\t\t  | atan2\n\t\t\t\t\t  | atanh\n\t\t\t\t\t  | ceil\n\t\t\t\t\t  | cos\n\t\t\t\t\t  | cosh\n\t\t\t\t\t  | exp\n\t\t\t\t\t  | expm1\n\t\t\t\t\t  | floor\n\t\t\t\t\t  | hypot\n\t\t\t\t\t  | log\n\t\t\t\t\t  | log10\n\t\t\t\t\t  | log1p\n\t\t\t\t\t  | log2\n\t\t\t\t\t  | max\n\t\t\t\t\t  | min\n\t\t\t\t\t  | pow\n\t\t\t\t\t  | random\n\t\t\t\t\t  | round\n\t\t\t\t\t  | sign\n\t\t\t\t\t  | sin\n\t\t\t\t\t  | sinh\n\t\t\t\t\t  | sqrt\n\t\t\t\t\t  | tan\n\t\t\t\t\t  | tanh\n\t\t\t\t\t  | trunc\n\t\t\t\t\t)\n\t\t\t\t)\\b", "name": "support.function.static.math.coffee"}, {"match": "(?x)\n\t\t\t\t(\n\t\t\t\t\t(?<=Number\\.)\n\t\t\t\t\t(\n\t\t\t\t\t\tis(Finite|Integer|NaN)\n\t\t\t\t\t  | toInteger\n\t\t\t\t\t)\n\t\t\t\t)\\b", "name": "support.function.static.number.coffee"}, {"match": "\\b(Infinity|NaN|undefined)\\b", "name": "constant.language.coffee"}, {"match": "\\;", "name": "punctuation.terminator.statement.coffee"}, {"match": ",", "name": "meta.delimiter.object.comma.coffee"}, {"match": "\\.", "name": "meta.delimiter.method.period.coffee"}, {"match": "\\{|\\}", "name": "meta.brace.curly.coffee"}, {"match": "\\(|\\)", "name": "meta.brace.round.coffee"}, {"match": "\\[|\\]\\s*", "name": "meta.brace.square.coffee"}, {"include": "#instance_variable"}, {"include": "#single_quoted_string"}, {"include": "#double_quoted_string"}, {"include": "#numeric"}], "repository": {"double_quoted_string": {"patterns": [{"begin": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.coffee"}}, "end": "\"", "endCaptures": {"0": {"name": "punctuation.definition.string.end.coffee"}}, "name": "string.quoted.double.coffee", "patterns": [{"match": "(?x)\n\t\t\t\t\t\t\t\t\\\\(\n\t\t\t\t\t\t\t\t\tx\\h{2}\n\t\t\t\t\t\t\t\t  | [0-2][0-7]{0,2}\n\t\t\t\t\t\t\t\t  | 3[0-6][0-7]\n\t\t\t\t\t\t\t\t  | 37[0-7]?\n\t\t\t\t\t\t\t\t  | [4-7][0-7]?\n\t\t\t\t\t\t\t\t  | .\n\t\t\t\t\t\t\t\t)", "name": "constant.character.escape.coffee"}, {"include": "#interpolated_coffee"}]}]}, "embedded_comment": {"patterns": [{"captures": {"1": {"name": "punctuation.definition.comment.coffee"}}, "match": "(?<!\\\\)(#).*$\\n?", "name": "comment.line.number-sign.coffee"}]}, "instance_variable": {"patterns": [{"captures": {"1": {"name": "punctuation.definition.variable.coffee"}}, "match": "(@)([a-zA-Z_\\$]\\w*)?", "name": "variable.other.readwrite.instance.coffee"}]}, "interpolated_coffee": {"patterns": [{"begin": "#\\{", "beginCaptures": {"0": {"name": "punctuation.section.embedded.begin.coffee"}}, "contentName": "source.coffee", "end": "(\\})", "endCaptures": {"0": {"name": "punctuation.section.embedded.end.coffee"}, "1": {"name": "source.coffee"}}, "name": "meta.embedded.line.coffee", "patterns": [{"include": "$self"}]}]}, "numeric": {"patterns": [{"match": "(?x)\n\t\t\t\t\t\t(?<!\\$)\\b\n\t\t\t\t\t\t(\n\t\t\t\t\t\t\t(0([box])[0-9a-fA-F]+)\n\t\t\t\t\t\t  | ([0-9]+(\\.[0-9]+)?(e[+\\-]?[0-9]+)?)\n\t\t\t\t\t\t)\\b", "name": "constant.numeric.coffee"}]}, "single_quoted_string": {"patterns": [{"begin": "'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.coffee"}}, "end": "'", "endCaptures": {"0": {"name": "punctuation.definition.string.end.coffee"}}, "name": "string.quoted.single.coffee", "patterns": [{"match": "(?x)\n\t\t\t\t\t\t\t\t\\\\(\n\t\t\t\t\t\t\t\t\tx\\h{2}\n\t\t\t\t\t\t\t\t  | [0-2][0-7]{0,2}\n\t\t\t\t\t\t\t\t  | 3[0-6][0-7]?\n\t\t\t\t\t\t\t\t  | 37[0-7]?\n\t\t\t\t\t\t\t\t  | [4-7][0-7]?\n\t\t\t\t\t\t\t\t  | .\n\t\t\t\t\t\t\t\t)", "name": "constant.character.escape.coffee"}]}]}, "variable_name": {"patterns": [{"match": "([a-zA-Z\\$_]\\w*(\\.\\w+)*)", "name": "variable.other.assignment.coffee"}]}}, "scopeName": "source.coffee", "uuid": "5B520980-A7D5-4E10-8582-1A4C889A8DE5", "version": "https://github.com/textmate/coffee-script.tmbundle/commit/da28450cf18a73595e298535c93c8370ae06994c"}