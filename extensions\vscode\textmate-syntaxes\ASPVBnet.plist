<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>comment</key>
	<string>Modified from the original ASP bundle. Originally modified by <PERSON> subtleGradient.com</string>
	<key>fileTypes</key>
	<array>
		<string>vb</string>
	</array>
	<key>keyEquivalent</key>
	<string>^~A</string>
	<key>name</key>
	<string>ASP vb.NET</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>match</key>
			<string>\n</string>
			<key>name</key>
			<string>meta.ending-space</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#round-brackets</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>^(?=\t)</string>
			<key>end</key>
			<string>(?=[^\t])</string>
			<key>name</key>
			<string>meta.leading-space</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>meta.odd-tab.tabs</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>meta.even-tab.tabs</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\t)(\t)?</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>^(?= )</string>
			<key>end</key>
			<string>(?=[^ ])</string>
			<key>name</key>
			<string>meta.leading-space</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>meta.odd-tab.spaces</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>meta.even-tab.spaces</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(  )(  )?</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.function.asp</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.asp</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.parameters.asp</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>variable.parameter.function.asp</string>
				</dict>
				<key>5</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.parameters.asp</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^\s*((?i:function|sub))\s*([a-zA-Z_]\w*)\s*(\()([^)]*)(\)).*\n?</string>
			<key>name</key>
			<string>meta.function.asp</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>(^[ \t]+)?(?=')</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.whitespace.comment.leading.asp</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?!\G)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>'</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.asp</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\n</string>
					<key>name</key>
					<string>comment.line.apostrophe.asp</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(If|Then|Else|ElseIf|Else If|End If|While|Wend|For|To|Each|Case|Select|End Select|Return|Continue|Do|Until|Loop|Next|With|Exit Do|Exit For|Exit Function|Exit Property|Exit Sub|IIf)\b)</string>
			<key>name</key>
			<string>keyword.control.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(Mod|And|Not|Or|Xor|as)\b)</string>
			<key>name</key>
			<string>keyword.operator.asp</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.asp</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>variable.other.bfeac.asp</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>meta.separator.comma.asp</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(?i:(dim)\s*(?:(\b[a-zA-Z_x7f-xff][a-zA-Z0-9_x7f-xff]*?\b)\s*(,?)))</string>
			<key>name</key>
			<string>variable.other.dim.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\s*\b(Call|Class|Const|Dim|Redim|Function|Sub|Private Sub|Public Sub|End sub|End Function|Set|Let|Get|New|Randomize|Option Explicit|On Error Resume Next|On Error GoTo)\b\s*)</string>
			<key>name</key>
			<string>storage.type.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(Private|Public|Default)\b)</string>
			<key>name</key>
			<string>storage.modifier.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\s*\b(Empty|False|Nothing|Null|True)\b)</string>
			<key>name</key>
			<string>constant.language.asp</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>"</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.asp</string>
				</dict>
			</dict>
			<key>end</key>
			<string>"</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.asp</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.asp</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>""</string>
					<key>name</key>
					<string>constant.character.escape.apostrophe.asp</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.variable.asp</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(\$)[a-zA-Z_x7f-xff][a-zA-Z0-9_x7f-xff]*?\b\s*</string>
			<key>name</key>
			<string>variable.other.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(Application|ObjectContext|Request|Response|Server|Session)\b)</string>
			<key>name</key>
			<string>support.class.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(Contents|StaticObjects|ClientCertificate|Cookies|Form|QueryString|ServerVariables)\b)</string>
			<key>name</key>
			<string>support.class.collection.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(TotalBytes|Buffer|CacheControl|Charset|ContentType|Expires|ExpiresAbsolute|IsClientConnected|PICS|Status|ScriptTimeout|CodePage|LCID|SessionID|Timeout)\b)</string>
			<key>name</key>
			<string>support.constant.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(Lock|Unlock|SetAbort|SetComplete|BinaryRead|AddHeader|AppendToLog|BinaryWrite|Clear|End|Flush|Redirect|Write|CreateObject|HTMLEncode|MapPath|URLEncode|Abandon|Convert|Regex)\b)</string>
			<key>name</key>
			<string>support.function.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(Application_OnEnd|Application_OnStart|OnTransactionAbort|OnTransactionCommit|Session_OnEnd|Session_OnStart)\b)</string>
			<key>name</key>
			<string>support.function.event.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:(?&lt;=as )(\b[a-zA-Z_x7f-xff][a-zA-Z0-9_x7f-xff]*?\b))</string>
			<key>name</key>
			<string>support.type.vb.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(Array|Add|Asc|Atn|CBool|CByte|CCur|CDate|CDbl|Chr|CInt|CLng|Conversions|Cos|CreateObject|CSng|CStr|Date|DateAdd|DateDiff|DatePart|DateSerial|DateValue|Day|Derived|Math|Escape|Eval|Exists|Exp|Filter|FormatCurrency|FormatDateTime|FormatNumber|FormatPercent|GetLocale|GetObject|GetRef|Hex|Hour|InputBox|InStr|InStrRev|Int|Fix|IsArray|IsDate|IsEmpty|IsNull|IsNumeric|IsObject|Item|Items|Join|Keys|LBound|LCase|Left|Len|LoadPicture|Log|LTrim|RTrim|Trim|Maths|Mid|Minute|Month|MonthName|MsgBox|Now|Oct|Remove|RemoveAll|Replace|RGB|Right|Rnd|Round|ScriptEngine|ScriptEngineBuildVersion|ScriptEngineMajorVersion|ScriptEngineMinorVersion|Second|SetLocale|Sgn|Sin|Space|Split|Sqr|StrComp|String|StrReverse|Tan|Time|Timer|TimeSerial|TimeValue|TypeName|UBound|UCase|Unescape|VarType|Weekday|WeekdayName|Year)\b)</string>
			<key>name</key>
			<string>support.function.vb.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>-?\b((0(x|X)[0-9a-fA-F]*)|(([0-9]+\.?[0-9]*)|(\.[0-9]+))((e|E)(\+|-)?[0-9]+)?)(L|l|UL|ul|u|U|F|f)?\b</string>
			<key>name</key>
			<string>constant.numeric.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:\b(vbtrue|vbfalse|vbcr|vbcrlf|vbformfeed|vblf|vbnewline|vbnullchar|vbnullstring|int32|vbtab|vbverticaltab|vbbinarycompare|vbtextcomparevbsunday|vbmonday|vbtuesday|vbwednesday|vbthursday|vbfriday|vbsaturday|vbusesystemdayofweek|vbfirstjan1|vbfirstfourdays|vbfirstfullweek|vbgeneraldate|vblongdate|vbshortdate|vblongtime|vbshorttime|vbobjecterror|vbEmpty|vbNull|vbInteger|vbLong|vbSingle|vbDouble|vbCurrency|vbDate|vbString|vbObject|vbError|vbBoolean|vbVariant|vbDataObject|vbDecimal|vbByte|vbArray)\b)</string>
			<key>name</key>
			<string>support.type.vb.asp</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.asp</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(?i:(\b[a-zA-Z_x7f-xff][a-zA-Z0-9_x7f-xff]*?\b)(?=\(\)?))</string>
			<key>name</key>
			<string>support.function.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(?i:((?&lt;=(\+|=|-|\&amp;|\\|/|&lt;|&gt;|\(|,))\s*\b([a-zA-Z_x7f-xff][a-zA-Z0-9_x7f-xff]*?)\b(?!(\(|\.))|\b([a-zA-Z_x7f-xff][a-zA-Z0-9_x7f-xff]*?)\b(?=\s*(\+|=|-|\&amp;|\\|/|&lt;|&gt;|\(|\)))))</string>
			<key>name</key>
			<string>variable.other.asp</string>
		</dict>
		<dict>
			<key>match</key>
			<string>!|\$|%|&amp;|\*|\-\-|\-|\+\+|\+|~|===|==|=|!=|!==|&lt;=|&gt;=|&lt;&lt;=|&gt;&gt;=|&gt;&gt;&gt;=|&lt;&gt;|&lt;|&gt;|!|&amp;&amp;|\|\||\?\:|\*=|/=|%=|\+=|\-=|&amp;=|\^=|\b(in|instanceof|new|delete|typeof|void)\b</string>
			<key>name</key>
			<string>keyword.operator.js</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>round-brackets</key>
		<dict>
			<key>begin</key>
			<string>\(</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.round-brackets.begin.asp</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\)</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.round-brackets.end.asp</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.round-brackets</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>$self</string>
				</dict>
			</array>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>source.asp.vb.net</string>
	<key>uuid</key>
	<string>7F9C9343-D48E-4E7D-BFE8-F680714DCD3E</string>
</dict>
</plist>