name: "Label / close stale issues"
on:
  schedule:
    - cron: "30 1 * * *"

jobs:
  stale:
    permissions:
      issues: write
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          close-issue-message: "This issue was closed because it wasn't updated for 10 days after being marked stale. If it's still important, please reopen + comment and we'll gladly take another look!"
          stale-issue-message: "This issue hasn't been updated in 90 days and will be closed after an additional 10 days without activity. If it's still important, please leave a comment and share any new information that would help us address the issue."
          days-before-stale: 90
          days-before-close: 10
          days-before-pr-stale: -1
          days-before-pr-close: -1
          operations-per-run: 1000
          stale-issue-label: "stale"
          stale-pr-label: "stale"
          exempt-issue-labels: "needs-triage,no-stale,high,highest"
          exempt-pr-labels: "no-stale"
