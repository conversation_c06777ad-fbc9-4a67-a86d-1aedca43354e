from fastapi import FastAPI

app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}

@app.get("/landing")
def read_landing_page():
    return {"message": "Welcome to the landing page"}

---

from fastapi import FastAPI, HTTPException

app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}

@app.get("/landing")
def read_landing_page():
    raise HTTPException(status_code=404, detail="Page not found")

---

- from fastapi import FastAPI
+ from fastapi import FastAPI, HTTPException
 
 app = FastAPI()
 
 @app.get("/")
 def read_root():
     return {"Hello": "World"}
 
 @app.get("/landing")
 def read_landing_page():
+     raise HTTPException(status_code=404, detail="Page not found")
-     return {"message": "Welcome to the landing page"}